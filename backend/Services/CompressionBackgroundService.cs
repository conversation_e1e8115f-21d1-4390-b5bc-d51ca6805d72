using Microsoft.EntityFrameworkCore;
using VidCompressor.Models;
using VidCompressor.Services;
using System.Diagnostics;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using VidCompressor.Hubs;

namespace VidCompressor.Services;

public class CompressionBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<CompressionBackgroundService> _logger;
    private readonly IHubContext<NotificationHub> _hubContext;
    private static readonly ActivitySource Activity = new ActivitySource("VidCompressor.CompressionService");

    public CompressionBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<CompressionBackgroundService> logger,
        IHubContext<NotificationHub> hubContext)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _hubContext = hubContext;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Monitor running transcoder jobs
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await MonitorTranscoderJobsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in transcoder job monitoring");
            }

            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken); // Check every 30 seconds
        }
    }

    public async Task ProcessCompressionJobAsync(string jobId, CancellationToken cancellationToken = default)
    {
        using var activity = Activity.StartActivity("ProcessCompressionJob");

        _logger.LogInformation("Processing compression job: {JobId}", jobId);
        activity?.SetTag("job.id", jobId);

        try
        {
            await ProcessCompressionJobInternalAsync(jobId, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task ProcessCompressionJobInternalAsync(string jobId, CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var storageService = scope.ServiceProvider.GetRequiredService<GoogleCloudStorageService>();
        var transcoderService = scope.ServiceProvider.GetRequiredService<GoogleTranscoderService>();
        var googlePhotosService = scope.ServiceProvider.GetRequiredService<GooglePhotosService>();
        var imageCompressionService = scope.ServiceProvider.GetRequiredService<ImageCompressionService>();

        var job = await context.CompressionJobs.FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
        if (job == null)
        {
            _logger.LogWarning("Compression job not found: {JobId}", jobId);
            return;
        }

        try
        {
            var accessToken = await GetUserAccessToken(context, job.UserId);
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new InvalidOperationException("Unable to get valid access token for user");
            }

            if (job.MediaType == MediaType.Video)
            {
                await ProcessVideoCompressionAsync(context, job, storageService, transcoderService, googlePhotosService, accessToken, cancellationToken);
            }
            else if (job.MediaType == MediaType.Photo)
            {
                await ProcessPhotoCompressionAsync(context, job, imageCompressionService, googlePhotosService, accessToken, cancellationToken);
            }
            else
            {
                throw new InvalidOperationException($"Unsupported media type: {job.MediaType}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task ProcessVideoCompressionAsync(
        ApplicationDbContext context,
        CompressionJob job,
        GoogleCloudStorageService storageService,
        GoogleTranscoderService transcoderService,
        GooglePhotosService googlePhotosService,
        string accessToken,
        CancellationToken cancellationToken)
    {
        // Step 1: Download video from Google Photos
        await UpdateJobStatus(context, job, CompressionJobStatus.DownloadingFromGooglePhotos);

        // Video dimensions should already be set from the PhotosPicker metadata
        if (!job.OriginalWidth.HasValue || !job.OriginalHeight.HasValue)
        {
            _logger.LogWarning("Video dimensions not available for job {JobId}, using defaults", job.Id);
        }

        using var videoStream = await googlePhotosService.DownloadVideoAsync(accessToken, job.MediaItemId, job.BaseUrl);
        var originalSize = videoStream.Length;
        job.OriginalSizeBytes = originalSize;

        // Step 2: Upload to Cloud Storage
        await UpdateJobStatus(context, job, CompressionJobStatus.UploadingToStorage);

        var inputFileName = $"{job.MediaItemId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}.mp4";
        var inputPath = await storageService.UploadToInputBucketAsync(videoStream, inputFileName);
        job.InputStoragePath = inputPath;

        // Step 3: Start transcoding
        await UpdateJobStatus(context, job, CompressionJobStatus.TranscodingInProgress);

        var expectedOutputPath = storageService.GenerateOutputPath(inputPath, job.Quality);

        (string transcoderJobName, string actualOutputPath) = await transcoderService.CreateTranscodingJobAsync(inputPath, expectedOutputPath, job.Quality, job.OriginalWidth, job.OriginalHeight);
        job.TranscoderJobName = transcoderJobName;
        job.OutputStoragePath = actualOutputPath;
        job.StartedAt = DateTime.UtcNow;

        await context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Started transcoding job {TranscoderJobName} for compression job {JobId}",
            transcoderJobName, job.Id);
    }

    private async Task ProcessPhotoCompressionAsync(
        ApplicationDbContext context,
        CompressionJob job,
        ImageCompressionService imageCompressionService,
        GooglePhotosService googlePhotosService,
        string accessToken,
        CancellationToken cancellationToken)
    {
        // Step 1: Download photo from Google Photos
        await UpdateJobStatus(context, job, CompressionJobStatus.DownloadingFromGooglePhotos);

        using var photoStream = await googlePhotosService.DownloadPhotoAsync(accessToken, job.MediaItemId, job.BaseUrl);
        var originalSize = photoStream.Length;
        job.OriginalSizeBytes = originalSize;

        // Step 2: Compress image directly (no cloud storage needed for photos)
        await UpdateJobStatus(context, job, CompressionJobStatus.CompressingImage);

        using var compressedStream = new MemoryStream();
        var compressionResult = await imageCompressionService.CompressImageAsync(
            photoStream, compressedStream, job.Quality, job.OriginalWidth, job.OriginalHeight);

        job.CompressedSizeBytes = compressionResult.CompressedSizeBytes;
        job.CompressionRatio = compressionResult.CompressionRatio;
        job.StartedAt = DateTime.UtcNow;

        // Step 3: Save compressed photo for batch upload if requested
        if (job.UploadToGooglePhotos)
        {
            // Save compressed photo to persistent temp file for batch upload
            var tempDir = Path.Combine(Path.GetTempPath(), "vidcompressor_batch");
            Directory.CreateDirectory(tempDir);

            var extension = job.Quality.ToLower() == "low" ? ".webp" : ".jpg";
            var tempFilePath = Path.Combine(tempDir, $"{job.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}{extension}");

            compressedStream.Position = 0;
            await using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
            {
                await compressedStream.CopyToAsync(fileStream, cancellationToken);
            }

            job.CompressedFilePath = tempFilePath;
            await UpdateJobStatus(context, job, CompressionJobStatus.ReadyForBatchUpload);

            _logger.LogInformation("Photo compression completed for job {JobId}, saved to {FilePath}, ready for batch upload",
                job.Id, tempFilePath);
        }
        else
        {
            // If not uploading to Google Photos, mark as completed
            await UpdateJobStatus(context, job, CompressionJobStatus.Completed);
            job.CompletedAt = DateTime.UtcNow;
        }
        await context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Completed photo compression job {JobId}. Original: {OriginalSize} bytes, Compressed: {CompressedSize} bytes, Ratio: {Ratio:P2}",
            job.Id, originalSize, job.CompressedSizeBytes, job.CompressionRatio);
    }

    private async Task MonitorTranscoderJobsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var transcoderService = scope.ServiceProvider.GetRequiredService<GoogleTranscoderService>();

        var runningJobs = await context.CompressionJobs
            .Where(j => j.Status == CompressionJobStatus.TranscodingInProgress &&
                       !string.IsNullOrEmpty(j.TranscoderJobName))
            .ToListAsync(cancellationToken);

        foreach (var job in runningJobs)
        {
            try
            {
                var status = await transcoderService.GetJobStatusAsync(job.TranscoderJobName!);

                if (status == TranscodingJobStatus.Succeeded)
                {
                    _logger.LogInformation("Transcoding completed for job {JobId}", job.Id);
                    await CompleteTranscodingAsync(job.Id, cancellationToken);
                }
                else if (status == TranscodingJobStatus.Failed)
                {
                    _logger.LogError("Transcoding failed for job {JobId}", job.Id);
                    var jobInfo = await transcoderService.GetJobInfoAsync(job.TranscoderJobName!);
                    await MarkJobAsFailed(job.Id, jobInfo.ErrorMessage ?? "Transcoding failed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to check transcoder job status for {JobId}", job.Id);
            }
        }
    }

    private async Task CompleteTranscodingAsync(string jobId, CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var storageService = scope.ServiceProvider.GetRequiredService<GoogleCloudStorageService>();
        var googlePhotosService = scope.ServiceProvider.GetRequiredService<GooglePhotosService>();

        var job = await context.CompressionJobs.FirstOrDefaultAsync(j => j.Id == jobId, cancellationToken);
        if (job == null) return;

        try
        {
            // Step 4: Verify file exists and download compressed video from storage
            await UpdateJobStatus(context, job, CompressionJobStatus.DownloadingFromStorage);

            // Check if the output file exists before attempting download
            var fileExists = await storageService.FileExistsAsync(job.OutputStoragePath!);
            if (!fileExists)
            {
                throw new InvalidOperationException($"Output file not found at expected path: {job.OutputStoragePath}");
            }

            _logger.LogInformation("Downloading compressed video from: {OutputPath}", job.OutputStoragePath);
            using var compressedVideoStream = await storageService.DownloadFromOutputBucketAsync(job.OutputStoragePath!);
            var compressedSize = compressedVideoStream.Length;
            job.CompressedSizeBytes = compressedSize;
            job.CompressionRatio = job.OriginalSizeBytes > 0 ? (double)compressedSize / job.OriginalSizeBytes : 0;

            // Step 5: Save compressed video for batch upload if requested
            if (job.UploadToGooglePhotos)
            {
                // Save compressed video to persistent temp file for batch upload
                var tempDir = Path.Combine(Path.GetTempPath(), "vidcompressor_batch");
                Directory.CreateDirectory(tempDir);

                var tempFilePath = Path.Combine(tempDir, $"{job.Id}_{DateTime.UtcNow:yyyyMMddHHmmss}.mp4");

                await using (var fileStream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await compressedVideoStream.CopyToAsync(fileStream, cancellationToken);
                }

                job.CompressedFilePath = tempFilePath;
                await UpdateJobStatus(context, job, CompressionJobStatus.ReadyForBatchUpload);

                _logger.LogInformation("Video compression completed for job {JobId}, saved to {FilePath}, ready for batch upload. " +
                    "Original size: {OriginalSize} bytes, Compressed size: {CompressedSize} bytes, " +
                    "Compression ratio: {CompressionRatio:P2}",
                    jobId, job.OriginalSizeBytes, job.CompressedSizeBytes, job.CompressionRatio, tempFilePath);
            }
            else
            {
                // If not uploading to Google Photos, mark as completed
                await UpdateJobStatus(context, job, CompressionJobStatus.Completed);
                job.CompletedAt = DateTime.UtcNow;

                _logger.LogInformation("Video compression completed for job {JobId} (no upload requested). " +
                    "Original size: {OriginalSize} bytes, Compressed size: {CompressedSize} bytes, " +
                    "Compression ratio: {CompressionRatio:P2}",
                    jobId, job.OriginalSizeBytes, job.CompressedSizeBytes, job.CompressionRatio);
            }

            // Clean up storage files
            await CleanupStorageFiles(storageService, job);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete compression job: {JobId}", jobId);
            await MarkJobAsFailed(jobId, ex.Message);
        }
    }

    private async Task UpdateJobStatus(ApplicationDbContext context, CompressionJob job, CompressionJobStatus status)
    {
        job.Status = status;
        await context.SaveChangesAsync();
        _logger.LogInformation("Updated job {JobId} status to {Status}", job.Id, status);

        // DEBUGGING: Always log this
        Console.WriteLine($"[DEBUG] UpdateJobStatus called for job {job.Id}, status: {status}, userId: {job.UserId}");

        // Send real-time status update to the user
        _logger.LogInformation("Sending SignalR update for job {JobId} to user {UserId}: {Status}",
            job.Id, job.UserId, status);

        try
        {
            await _hubContext.Clients.Group($"User_{job.UserId}").SendAsync("CompressionStatusUpdate", new
            {
                jobId = job.Id,
                mediaItemId = job.MediaItemId,
                status = status.ToString(),
                message = GetStatusMessage(status),
                progress = GetProgressPercentage(status)
            });

            // Also try sending to all clients as a fallback for debugging
            await _hubContext.Clients.All.SendAsync("CompressionStatusUpdate", new
            {
                jobId = job.Id,
                mediaItemId = job.MediaItemId,
                status = status.ToString(),
                message = GetStatusMessage(status),
                progress = GetProgressPercentage(status),
                userId = job.UserId // Include userId for debugging
            });

            _logger.LogInformation("SignalR update sent successfully for job {JobId}", job.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SignalR update for job {JobId}", job.Id);
        }
    }

    private static string GetStatusMessage(CompressionJobStatus status)
    {
        return status switch
        {
            CompressionJobStatus.Queued => "Queued for processing",
            CompressionJobStatus.DownloadingFromGooglePhotos => "Downloading from Google Photos",
            CompressionJobStatus.UploadingToStorage => "Uploading to cloud storage",
            CompressionJobStatus.TranscodingInProgress => "Compressing video",
            CompressionJobStatus.CompressingImage => "Compressing photo",
            CompressionJobStatus.DownloadingFromStorage => "Downloading compressed media",
            CompressionJobStatus.ReadyForBatchUpload => "Ready for upload",
            CompressionJobStatus.UploadingToGooglePhotos => "Uploading to Google Photos",
            CompressionJobStatus.DeletingOriginal => "Deleting original media",
            CompressionJobStatus.Completed => "Compression completed",
            CompressionJobStatus.Failed => "Compression failed",
            CompressionJobStatus.Cancelled => "Compression cancelled",
            _ => "Processing"
        };
    }

    private static int GetProgressPercentage(CompressionJobStatus status)
    {
        return status switch
        {
            CompressionJobStatus.Queued => 0,
            CompressionJobStatus.DownloadingFromGooglePhotos => 10,
            CompressionJobStatus.UploadingToStorage => 20,
            CompressionJobStatus.TranscodingInProgress => 30,      // For videos
            CompressionJobStatus.CompressingImage => 30,           // For photos - same progress as video transcoding
            CompressionJobStatus.DownloadingFromStorage => 70,
            CompressionJobStatus.ReadyForBatchUpload => 80,        // Compression complete, waiting for upload
            CompressionJobStatus.UploadingToGooglePhotos => 90,
            CompressionJobStatus.DeletingOriginal => 95,
            CompressionJobStatus.Completed => 100,
            CompressionJobStatus.Failed => 0,
            CompressionJobStatus.Cancelled => 0,
            _ => 0
        };
    }

    private async Task MarkJobAsFailed(string jobId, string errorMessage)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        var job = await context.CompressionJobs.FirstOrDefaultAsync(j => j.Id == jobId);
        if (job != null)
        {
            job.Status = CompressionJobStatus.Failed;
            job.ErrorMessage = errorMessage;
            job.CompletedAt = DateTime.UtcNow;
            await context.SaveChangesAsync();

            // Send failure notification
            _logger.LogInformation("Sending failure notification for job {JobId} to user {UserId}",
                job.Id, job.UserId);

            try
            {
                await _hubContext.Clients.Group($"User_{job.UserId}").SendAsync("CompressionStatusUpdate", new
                {
                    jobId = job.Id,
                    mediaItemId = job.MediaItemId,
                    status = CompressionJobStatus.Failed.ToString(),
                    message = $"Compression failed: {errorMessage}",
                    progress = 0,
                    error = errorMessage
                });

                // Also send to all clients for debugging
                await _hubContext.Clients.All.SendAsync("CompressionStatusUpdate", new
                {
                    jobId = job.Id,
                    mediaItemId = job.MediaItemId,
                    status = CompressionJobStatus.Failed.ToString(),
                    message = $"Compression failed: {errorMessage}",
                    progress = 0,
                    error = errorMessage,
                    userId = job.UserId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send failure notification for job {JobId}", job.Id);
            }
        }
    }

    private async Task<string?> GetUserAccessToken(ApplicationDbContext context, string userId)
    {
        var user = await context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user == null) return null;

        // Check if token is expired and refresh if needed
        if (user.GoogleTokenExpiry <= DateTime.UtcNow && !string.IsNullOrEmpty(user.GoogleRefreshToken))
        {
            // Token refresh logic would go here
            // For now, return the existing token
        }

        return user.GoogleAccessToken;
    }

    private async Task CleanupStorageFiles(GoogleCloudStorageService storageService, CompressionJob job)
    {
        try
        {
            if (!string.IsNullOrEmpty(job.InputStoragePath))
            {
                await storageService.DeleteFileAsync(job.InputStoragePath);
            }
            if (!string.IsNullOrEmpty(job.OutputStoragePath))
            {
                await storageService.DeleteFileAsync(job.OutputStoragePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cleanup storage files for job {JobId}", job.Id);
        }
    }
}
